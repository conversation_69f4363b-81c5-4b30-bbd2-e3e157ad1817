{"logs": [{"outputFile": "com.example.planter.app-mergeDebugResources-34:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,9369", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,9450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3996,4106,9455", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3580,3682,3781,3881,3991,4101,4221,9551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4a4e76919eb039bb1b10323e92398cd9\\transformed\\material-1.11.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4226,4327,4447,4528,4592,4684,4763,4823,4913,4977,5048,5111,5186,5250,5304,5431,5489,5551,5605,5684,5825,5912,5994,6133,6216,6300,6439,6526,6606,6662,6713,6779,6853,6933,7020,7103,7176,7253,7322,7396,7498,7586,7663,7756,7852,7926,8006,8103,8155,8239,8305,8392,8480,8542,8606,8669,8737,8849,8960,9067,9177,9237,9292", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3137,3215,3299,3394,3483,4322,4442,4523,4587,4679,4758,4818,4908,4972,5043,5106,5181,5245,5299,5426,5484,5546,5600,5679,5820,5907,5989,6128,6211,6295,6434,6521,6601,6657,6708,6774,6848,6928,7015,7098,7171,7248,7317,7391,7493,7581,7658,7751,7847,7921,8001,8098,8150,8234,8300,8387,8475,8537,8601,8664,8732,8844,8955,9062,9172,9232,9287,9364"}}]}]}