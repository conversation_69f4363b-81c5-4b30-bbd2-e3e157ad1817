# Bluetooth Car Controller

A cyberpunk-styled Android app for controlling a car via Bluetooth Classic connection to ESP32.

## Features

### 🎮 Controls
- **Left Joystick**: Forward/Backward movement
- **Right Joystick**: Left/Right steering
- **Side Buttons**: Horn, Lights, Stop, Connect
- **Hold Behavior**: All controls work with hold - continuously send commands while pressed, send "S" (Stop) when released

### 🎨 Design
- **Landscape Orientation**: App always runs in landscape mode
- **Cyberpunk Theme**: Dark background with cyber green (#00FF7F) glowing buttons and joysticks
- **Futuristic Styling**: Neon green borders with glow effects

### 📡 Bluetooth
- **Bluetooth Classic**: Connects to ESP32 using SPP (Serial Port Profile)
- **Connection Status**: Shows "Connected" when paired with ESP32
- **Real-time Data**: Displays received serial data from ESP32 in scrollable text area

## Commands Sent

| Action | Command |
|--------|---------|
| Forward | 'F' |
| Backward | 'B' |
| Left | 'L' |
| Right | 'R' |
| Stop (on release) | 'S' |
| Horn | 'H' |
| Lights | 'P' |

## Setup Instructions

### 1. Android App
1. Build and install the APK on your Android device (API level 25+)
2. Grant Bluetooth permissions when prompted
3. Pair your ESP32 device in Android Bluetooth settings first
4. Open the app and tap "CONNECT" to select your ESP32

### 2. ESP32 Code (Basic Example)
```cpp
#include "BluetoothSerial.h"

BluetoothSerial SerialBT;

void setup() {
  Serial.begin(115200);
  SerialBT.begin("ESP32_Car"); // Bluetooth device name
  Serial.println("The device started, now you can pair it with bluetooth!");
}

void loop() {
  if (SerialBT.available()) {
    char command = SerialBT.read();
    
    switch(command) {
      case 'F': // Forward
        Serial.println("Moving Forward");
        // Add your motor control code here
        break;
      case 'B': // Backward
        Serial.println("Moving Backward");
        break;
      case 'L': // Left
        Serial.println("Turning Left");
        break;
      case 'R': // Right
        Serial.println("Turning Right");
        break;
      case 'S': // Stop
        Serial.println("Stopping");
        break;
      case 'H': // Horn
        Serial.println("Horn!");
        break;
      case 'P': // Lights
        Serial.println("Lights!");
        break;
    }
    
    // Send acknowledgment back to app
    SerialBT.println("Command received: " + String(command));
  }
}
```

## Technical Details

### Architecture
- **MainActivity.kt**: UI logic and Bluetooth integration
- **BluetoothService.kt**: Handles all Bluetooth Classic communication
- **JoystickView.kt**: Custom virtual joystick with cyberpunk styling
- **activity_main.xml**: Landscape layout with joysticks and buttons

### Permissions Required
- `BLUETOOTH`
- `BLUETOOTH_ADMIN`
- `BLUETOOTH_CONNECT` (Android 12+)
- `BLUETOOTH_SCAN` (Android 12+)
- `ACCESS_FINE_LOCATION`

### Key Features
- **Thread-safe**: UI updates happen on main thread
- **Hold behavior**: Continuous command sending while controls are held
- **Auto-reconnect**: Handles connection drops gracefully
- **Real-time feedback**: Shows received data from ESP32

## Usage
1. Ensure ESP32 is powered and Bluetooth is enabled
2. Pair ESP32 with your Android device
3. Open the app and connect to your ESP32
4. Use joysticks and buttons to control your car
5. Monitor ESP32 responses in the data area at the bottom

## Requirements
- Android device with API level 25+ (Android 7.1)
- ESP32 with Bluetooth Classic support
- Paired Bluetooth connection between devices
