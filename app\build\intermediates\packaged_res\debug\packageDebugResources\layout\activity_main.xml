<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0a0a0a"
    android:padding="16dp">

    <!-- Status Bar -->
    <LinearLayout
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/tvConnectionStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Disconnected"
            android:textColor="#00FF7F"
            android:textSize="16sp"
            android:textStyle="bold"
            android:shadowColor="#00FF7F"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5" />

        <Button
            android:id="@+id/btnConnect"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="CONNECT"
            android:textColor="#00FF7F"
            android:textStyle="bold"
            android:background="@drawable/cyber_button"
            android:layout_marginStart="16dp" />

    </LinearLayout>

    <!-- Main Control Area -->
    <LinearLayout
        android:id="@+id/controlArea"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/statusBar"
        android:layout_above="@id/dataArea"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <!-- Left Joystick (Forward/Backward) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="MOVEMENT"
                android:textColor="#00FF7F"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"
                android:shadowColor="#00FF7F"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3" />

            <com.example.planter.JoystickView
                android:id="@+id/leftJoystick"
                android:layout_width="200dp"
                android:layout_height="200dp" />

        </LinearLayout>

        <!-- Side Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginHorizontal="24dp">

            <Button
                android:id="@+id/btnHorn"
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:text="HORN"
                android:textColor="#00FF7F"
                android:textStyle="bold"
                android:background="@drawable/cyber_button"
                android:layout_marginBottom="16dp" />

            <Button
                android:id="@+id/btnLights"
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:text="LIGHTS"
                android:textColor="#00FF7F"
                android:textStyle="bold"
                android:background="@drawable/cyber_button"
                android:layout_marginBottom="16dp" />

            <Button
                android:id="@+id/btnStop"
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:text="STOP"
                android:textColor="#FF4444"
                android:textStyle="bold"
                android:background="@drawable/cyber_button_red"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

        <!-- Right Joystick (Left/Right) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="STEERING"
                android:textColor="#00FF7F"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"
                android:shadowColor="#00FF7F"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3" />

            <com.example.planter.JoystickView
                android:id="@+id/rightJoystick"
                android:layout_width="200dp"
                android:layout_height="200dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Data Display Area -->
    <LinearLayout
        android:id="@+id/dataArea"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:padding="8dp"
        android:background="#1a1a1a">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ESP32 DATA:"
            android:textColor="#00FF7F"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tvReceivedData"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No data received..."
                android:textColor="#00FF7F"
                android:textSize="11sp"
                android:fontFamily="monospace"
                android:padding="4dp" />

        </ScrollView>

    </LinearLayout>

</RelativeLayout>
