1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.planter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="36" />
10
11    <!-- Bluetooth permissions -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
14-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- For Android 12+ (API 31+) -->
18    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
18-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:12:5-73
18-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:12:22-70
19
20    <uses-feature
20-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:14:5-16:35
21        android:name="android.hardware.bluetooth"
21-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:15:9-50
22        android:required="true" />
22-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:16:9-32
23
24    <permission
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
25        android:name="com.example.planter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.example.planter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
29
30    <application
30-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:18:5-39:19
31        android:allowBackup="true"
31-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:19:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
33        android:dataExtractionRules="@xml/data_extraction_rules"
33-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:20:9-65
34        android:debuggable="true"
35        android:extractNativeLibs="false"
36        android:fullBackupContent="@xml/backup_rules"
36-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:21:9-54
37        android:icon="@mipmap/ic_launcher"
37-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:22:9-43
38        android:label="@string/app_name"
38-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:23:9-41
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:24:9-54
40        android:supportsRtl="true"
40-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:25:9-35
41        android:theme="@style/Theme.Planter" >
41-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:26:9-45
42        <activity
42-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:27:9-38:20
43            android:name="com.example.planter.MainActivity"
43-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:28:13-41
44            android:exported="true"
44-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:29:13-36
45            android:label="@string/app_name"
45-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:30:13-45
46            android:screenOrientation="landscape"
46-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:31:13-50
47            android:theme="@style/Theme.Planter" >
47-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:32:13-49
48            <intent-filter>
48-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:33:13-37:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:34:17-69
49-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:34:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:36:17-77
51-->C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:36:27-74
52            </intent-filter>
53        </activity>
54
55        <provider
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.example.planter.androidx-startup"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
