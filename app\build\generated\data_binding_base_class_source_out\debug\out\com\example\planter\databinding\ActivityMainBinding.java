// Generated by view binder compiler. Do not edit!
package com.example.planter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.planter.JoystickView;
import com.example.planter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button btnConnect;

  @NonNull
  public final Button btnHorn;

  @NonNull
  public final Button btnLights;

  @NonNull
  public final Button btnStop;

  @NonNull
  public final LinearLayout controlArea;

  @NonNull
  public final LinearLayout dataArea;

  @NonNull
  public final JoystickView leftJoystick;

  @NonNull
  public final JoystickView rightJoystick;

  @NonNull
  public final LinearLayout statusBar;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvReceivedData;

  private ActivityMainBinding(@NonNull RelativeLayout rootView, @NonNull Button btnConnect,
      @NonNull Button btnHorn, @NonNull Button btnLights, @NonNull Button btnStop,
      @NonNull LinearLayout controlArea, @NonNull LinearLayout dataArea,
      @NonNull JoystickView leftJoystick, @NonNull JoystickView rightJoystick,
      @NonNull LinearLayout statusBar, @NonNull TextView tvConnectionStatus,
      @NonNull TextView tvReceivedData) {
    this.rootView = rootView;
    this.btnConnect = btnConnect;
    this.btnHorn = btnHorn;
    this.btnLights = btnLights;
    this.btnStop = btnStop;
    this.controlArea = controlArea;
    this.dataArea = dataArea;
    this.leftJoystick = leftJoystick;
    this.rightJoystick = rightJoystick;
    this.statusBar = statusBar;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvReceivedData = tvReceivedData;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnConnect;
      Button btnConnect = ViewBindings.findChildViewById(rootView, id);
      if (btnConnect == null) {
        break missingId;
      }

      id = R.id.btnHorn;
      Button btnHorn = ViewBindings.findChildViewById(rootView, id);
      if (btnHorn == null) {
        break missingId;
      }

      id = R.id.btnLights;
      Button btnLights = ViewBindings.findChildViewById(rootView, id);
      if (btnLights == null) {
        break missingId;
      }

      id = R.id.btnStop;
      Button btnStop = ViewBindings.findChildViewById(rootView, id);
      if (btnStop == null) {
        break missingId;
      }

      id = R.id.controlArea;
      LinearLayout controlArea = ViewBindings.findChildViewById(rootView, id);
      if (controlArea == null) {
        break missingId;
      }

      id = R.id.dataArea;
      LinearLayout dataArea = ViewBindings.findChildViewById(rootView, id);
      if (dataArea == null) {
        break missingId;
      }

      id = R.id.leftJoystick;
      JoystickView leftJoystick = ViewBindings.findChildViewById(rootView, id);
      if (leftJoystick == null) {
        break missingId;
      }

      id = R.id.rightJoystick;
      JoystickView rightJoystick = ViewBindings.findChildViewById(rootView, id);
      if (rightJoystick == null) {
        break missingId;
      }

      id = R.id.statusBar;
      LinearLayout statusBar = ViewBindings.findChildViewById(rootView, id);
      if (statusBar == null) {
        break missingId;
      }

      id = R.id.tvConnectionStatus;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tvReceivedData;
      TextView tvReceivedData = ViewBindings.findChildViewById(rootView, id);
      if (tvReceivedData == null) {
        break missingId;
      }

      return new ActivityMainBinding((RelativeLayout) rootView, btnConnect, btnHorn, btnLights,
          btnStop, controlArea, dataArea, leftJoystick, rightJoystick, statusBar,
          tvConnectionStatus, tvReceivedData);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
