package com.example.planter

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import kotlin.math.*

/**
 * Custom JoystickView for car controller
 * Provides virtual joystick with cyberpunk styling
 */
class JoystickView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Joystick properties
    private var centerX = 0f
    private var centerY = 0f
    private var baseRadius = 0f
    private var knobRadius = 0f
    private var knobX = 0f
    private var knobY = 0f
    
    // Touch tracking
    private var isDragging = false
    
    // Colors - Cyber green theme
    private val cyberGreen = Color.parseColor("#00FF7F")
    private val darkBackground = Color.parseColor("#1a1a1a")
    
    // Paint objects
    private val basePaint = Paint().apply {
        color = cyberGreen
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
        setShadowLayer(10f, 0f, 0f, cyberGreen)
    }
    
    private val knobPaint = Paint().apply {
        color = cyberGreen
        style = Paint.Style.FILL
        isAntiAlias = true
        setShadowLayer(15f, 0f, 0f, cyberGreen)
    }
    
    private val backgroundPaint = Paint().apply {
        color = darkBackground
        style = Paint.Style.FILL
        isAntiAlias = true
    }
    
    // Joystick listener
    interface JoystickListener {
        fun onJoystickMoved(xPercent: Float, yPercent: Float)
        fun onJoystickReleased()
    }
    
    private var listener: JoystickListener? = null
    
    fun setJoystickListener(listener: JoystickListener) {
        this.listener = listener
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        centerX = w / 2f
        centerY = h / 2f
        baseRadius = min(w, h) / 2f - 20f
        knobRadius = baseRadius / 3f
        
        // Initialize knob at center
        knobX = centerX
        knobY = centerY
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // Draw background circle
        canvas.drawCircle(centerX, centerY, baseRadius, backgroundPaint)
        
        // Draw base circle (outer ring)
        canvas.drawCircle(centerX, centerY, baseRadius, basePaint)
        
        // Draw knob
        canvas.drawCircle(knobX, knobY, knobRadius, knobPaint)
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                val distance = sqrt((event.x - centerX).pow(2) + (event.y - centerY).pow(2))
                if (distance <= baseRadius) {
                    isDragging = true
                    updateKnobPosition(event.x, event.y)
                    return true
                }
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    updateKnobPosition(event.x, event.y)
                    return true
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    // Return knob to center
                    knobX = centerX
                    knobY = centerY
                    invalidate()
                    listener?.onJoystickReleased()
                    return true
                }
            }
        }
        return false
    }
    
    private fun updateKnobPosition(x: Float, y: Float) {
        val deltaX = x - centerX
        val deltaY = y - centerY
        val distance = sqrt(deltaX.pow(2) + deltaY.pow(2))
        
        if (distance <= baseRadius) {
            knobX = x
            knobY = y
        } else {
            // Constrain knob to base circle
            val angle = atan2(deltaY, deltaX)
            knobX = centerX + cos(angle) * baseRadius
            knobY = centerY + sin(angle) * baseRadius
        }
        
        // Calculate percentages (-1 to 1)
        val xPercent = (knobX - centerX) / baseRadius
        val yPercent = (knobY - centerY) / baseRadius
        
        invalidate()
        listener?.onJoystickMoved(xPercent, yPercent)
    }
}
