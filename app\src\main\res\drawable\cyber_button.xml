<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#00AA5F" />
            <stroke 
                android:width="3dp" 
                android:color="#00FF7F" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#001a0a" />
            <stroke 
                android:width="2dp" 
                android:color="#00FF7F" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
