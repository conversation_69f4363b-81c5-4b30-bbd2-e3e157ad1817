#Sun Aug 17 01:13:18 EEST 2025
com.example.planter.app-main-38\:/drawable/cyber_button.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_cyber_button.xml.flat
com.example.planter.app-main-38\:/drawable/cyber_button_red.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_cyber_button_red.xml.flat
com.example.planter.app-main-38\:/drawable/ic_launcher_background.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.planter.app-main-38\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.planter.app-main-38\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.planter.app-main-38\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.planter.app-main-38\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.planter.app-main-38\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.planter.app-main-38\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.planter.app-main-38\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.planter.app-main-38\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.planter.app-main-38\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.planter.app-main-38\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.planter.app-main-38\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.planter.app-main-38\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.planter.app-main-38\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.planter.app-main-38\:/xml/backup_rules.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.planter.app-main-38\:/xml/data_extraction_rules.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.planter.app-mergeDebugResources-35\:/layout/activity_main.xml=C\:\\Users\\adams\\AndroidStudioProjects\\Planter\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
