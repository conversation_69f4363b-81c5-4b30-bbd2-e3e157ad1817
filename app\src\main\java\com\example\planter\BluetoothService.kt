package com.example.planter

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.*

/**
 * BluetoothService handles all Bluetooth Classic communication with ESP32
 * Manages connection, sending commands, and receiving data
 */
class BluetoothService(private val context: Context) {
    
    companion object {
        private const val TAG = "BluetoothService"
        private val SPP_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }
    
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothSocket: BluetoothSocket? = null
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    private var isConnected = false
    
    // Handler for UI thread updates
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // Listener interface for callbacks
    interface BluetoothListener {
        fun onConnectionStatusChanged(isConnected: Boolean)
        fun onDataReceived(data: String)
        fun onError(error: String)
    }
    
    private var listener: BluetoothListener? = null
    
    init {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    }
    
    fun setBluetoothListener(listener: BluetoothListener) {
        this.listener = listener
    }
    
    /**
     * Check if Bluetooth is available and enabled
     */
    fun isBluetoothAvailable(): Boolean {
        return bluetoothAdapter != null && bluetoothAdapter!!.isEnabled
    }
    
    /**
     * Get list of paired Bluetooth devices
     */
    fun getPairedDevices(): List<BluetoothDevice> {
        if (!isBluetoothAvailable()) return emptyList()
        
        return if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.BLUETOOTH_CONNECT
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            bluetoothAdapter?.bondedDevices?.toList() ?: emptyList()
        } else {
            emptyList()
        }
    }
    
    /**
     * Connect to a Bluetooth device
     */
    fun connectToDevice(device: BluetoothDevice) {
        Thread {
            try {
                if (ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    mainHandler.post {
                        listener?.onError("Bluetooth permission not granted")
                    }
                    return@Thread
                }
                
                // Close existing connection
                disconnect()
                
                // Create socket
                bluetoothSocket = device.createRfcommSocketToServiceRecord(SPP_UUID)
                
                // Cancel discovery to improve connection speed
                bluetoothAdapter?.cancelDiscovery()
                
                // Connect
                bluetoothSocket?.connect()
                
                // Get streams
                inputStream = bluetoothSocket?.inputStream
                outputStream = bluetoothSocket?.outputStream
                
                isConnected = true
                
                mainHandler.post {
                    listener?.onConnectionStatusChanged(true)
                }
                
                // Start listening for incoming data
                startListening()
                
                Log.d(TAG, "Connected to ${device.name}")
                
            } catch (e: IOException) {
                Log.e(TAG, "Connection failed", e)
                mainHandler.post {
                    listener?.onError("Connection failed: ${e.message}")
                    listener?.onConnectionStatusChanged(false)
                }
                disconnect()
            }
        }.start()
    }
    
    /**
     * Disconnect from current device
     */
    fun disconnect() {
        isConnected = false
        
        try {
            inputStream?.close()
            outputStream?.close()
            bluetoothSocket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing connection", e)
        }
        
        inputStream = null
        outputStream = null
        bluetoothSocket = null
        
        mainHandler.post {
            listener?.onConnectionStatusChanged(false)
        }
    }
    
    /**
     * Send a command to the connected device
     */
    fun sendCommand(command: String) {
        if (!isConnected || outputStream == null) {
            Log.w(TAG, "Not connected, cannot send command: $command")
            return
        }
        
        Thread {
            try {
                outputStream?.write(command.toByteArray())
                outputStream?.flush()
                Log.d(TAG, "Sent command: $command")
            } catch (e: IOException) {
                Log.e(TAG, "Error sending command", e)
                mainHandler.post {
                    listener?.onError("Failed to send command: ${e.message}")
                }
                disconnect()
            }
        }.start()
    }
    
    /**
     * Start listening for incoming data
     */
    private fun startListening() {
        Thread {
            val buffer = ByteArray(1024)
            
            while (isConnected && inputStream != null) {
                try {
                    val bytesRead = inputStream!!.read(buffer)
                    if (bytesRead > 0) {
                        val receivedData = String(buffer, 0, bytesRead)
                        Log.d(TAG, "Received: $receivedData")
                        
                        mainHandler.post {
                            listener?.onDataReceived(receivedData)
                        }
                    }
                } catch (e: IOException) {
                    Log.e(TAG, "Error reading data", e)
                    if (isConnected) {
                        mainHandler.post {
                            listener?.onError("Connection lost: ${e.message}")
                        }
                        disconnect()
                    }
                    break
                }
            }
        }.start()
    }
    
    /**
     * Check if currently connected
     */
    fun isConnected(): Boolean = isConnected
}
