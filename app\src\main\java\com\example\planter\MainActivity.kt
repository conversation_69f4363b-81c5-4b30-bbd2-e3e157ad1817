package com.example.planter

import android.Manifest
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * MainActivity - Bluetooth Car Controller
 * Provides cyberpunk-styled UI for controlling a car via Bluetooth
 */
class MainActivity : AppCompatActivity(), BluetoothService.BluetoothListener {

    companion object {
        private const val REQUEST_ENABLE_BT = 1
        private const val REQUEST_BLUETOOTH_PERMISSIONS = 2
    }

    // UI Components
    private lateinit var tvConnectionStatus: TextView
    private lateinit var btnConnect: Button
    private lateinit var leftJoystick: JoystickView
    private lateinit var rightJoystick: JoystickView
    private lateinit var btnHorn: Button
    private lateinit var btnLights: Button
    private lateinit var btnStop: Button
    private lateinit var tvReceivedData: TextView

    // Bluetooth service
    private lateinit var bluetoothService: BluetoothService

    // Command sending
    private val commandHandler = Handler(Looper.getMainLooper())
    private var currentCommand = ""
    private var isHoldingButton = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initializeViews()
        initializeBluetooth()
        setupJoysticks()
        setupButtons()
        checkBluetoothPermissions()
    }

    private fun initializeViews() {
        tvConnectionStatus = findViewById(R.id.tvConnectionStatus)
        btnConnect = findViewById(R.id.btnConnect)
        leftJoystick = findViewById(R.id.leftJoystick)
        rightJoystick = findViewById(R.id.rightJoystick)
        btnHorn = findViewById(R.id.btnHorn)
        btnLights = findViewById(R.id.btnLights)
        btnStop = findViewById(R.id.btnStop)
        tvReceivedData = findViewById(R.id.tvReceivedData)
    }

    private fun initializeBluetooth() {
        bluetoothService = BluetoothService(this)
        bluetoothService.setBluetoothListener(this)

        btnConnect.setOnClickListener {
            if (bluetoothService.isConnected()) {
                bluetoothService.disconnect()
            } else {
                showDeviceSelectionDialog()
            }
        }
    }

    private fun setupJoysticks() {
        // Left joystick for forward/backward movement
        leftJoystick.setJoystickListener(object : JoystickView.JoystickListener {
            override fun onJoystickMoved(xPercent: Float, yPercent: Float) {
                // Y-axis controls forward/backward (inverted because up is negative)
                when {
                    yPercent < -0.3f -> sendCommand("F") // Forward
                    yPercent > 0.3f -> sendCommand("B")  // Backward
                    else -> sendCommand("S") // Stop
                }
            }

            override fun onJoystickReleased() {
                sendCommand("S") // Stop
            }
        })

        // Right joystick for left/right steering
        rightJoystick.setJoystickListener(object : JoystickView.JoystickListener {
            override fun onJoystickMoved(xPercent: Float, yPercent: Float) {
                // X-axis controls left/right
                when {
                    xPercent < -0.3f -> sendCommand("L") // Left
                    xPercent > 0.3f -> sendCommand("R")  // Right
                    else -> sendCommand("S") // Stop
                }
            }

            override fun onJoystickReleased() {
                sendCommand("S") // Stop
            }
        })
    }

    private fun setupButtons() {
        // Horn button - hold behavior
        setupHoldButton(btnHorn, "H")

        // Lights button - hold behavior
        setupHoldButton(btnLights, "P")

        // Stop button - immediate stop
        btnStop.setOnClickListener {
            sendCommand("S")
        }
    }

    private fun setupHoldButton(button: Button, command: String) {
        button.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isHoldingButton = true
                    sendCommand(command)
                    true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    isHoldingButton = false
                    sendCommand("S") // Stop when released
                    true
                }
                else -> false
            }
        }
    }

    private fun sendCommand(command: String) {
        if (command != currentCommand) {
            currentCommand = command
            bluetoothService.sendCommand(command)
        }
    }

    private fun checkBluetoothPermissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.BLUETOOTH_CONNECT)
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.BLUETOOTH_SCAN)
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (permissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, permissions.toTypedArray(), REQUEST_BLUETOOTH_PERMISSIONS)
        } else {
            checkBluetoothEnabled()
        }
    }

    private fun checkBluetoothEnabled() {
        if (!bluetoothService.isBluetoothAvailable()) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT)
                == PackageManager.PERMISSION_GRANTED) {
                startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
            }
        }
    }

    private fun showDeviceSelectionDialog() {
        val pairedDevices = bluetoothService.getPairedDevices()

        if (pairedDevices.isEmpty()) {
            AlertDialog.Builder(this)
                .setTitle("No Paired Devices")
                .setMessage("Please pair your ESP32 device in Bluetooth settings first.")
                .setPositiveButton("OK", null)
                .show()
            return
        }

        val deviceNames = pairedDevices.map { device ->
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT)
                == PackageManager.PERMISSION_GRANTED) {
                device.name ?: "Unknown Device"
            } else {
                "Unknown Device"
            }
        }.toTypedArray()

        AlertDialog.Builder(this)
            .setTitle("Select ESP32 Device")
            .setItems(deviceNames) { _, which ->
                val selectedDevice = pairedDevices[which]
                bluetoothService.connectToDevice(selectedDevice)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    // BluetoothService.BluetoothListener implementation
    override fun onConnectionStatusChanged(isConnected: Boolean) {
        runOnUiThread {
            if (isConnected) {
                tvConnectionStatus.text = "Connected"
                btnConnect.text = "DISCONNECT"
            } else {
                tvConnectionStatus.text = "Disconnected"
                btnConnect.text = "CONNECT"
            }
        }
    }

    override fun onDataReceived(data: String) {
        runOnUiThread {
            val currentText = tvReceivedData.text.toString()
            val newText = if (currentText == "No data received...") {
                data
            } else {
                "$currentText\n$data"
            }
            tvReceivedData.text = newText

            // Auto-scroll to bottom
            val scrollView = tvReceivedData.parent as? View
            scrollView?.post {
                (scrollView.parent as? android.widget.ScrollView)?.fullScroll(View.FOCUS_DOWN)
            }
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            AlertDialog.Builder(this)
                .setTitle("Bluetooth Error")
                .setMessage(error)
                .setPositiveButton("OK", null)
                .show()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_BLUETOOTH_PERMISSIONS -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    checkBluetoothEnabled()
                } else {
                    AlertDialog.Builder(this)
                        .setTitle("Permissions Required")
                        .setMessage("Bluetooth permissions are required for this app to function.")
                        .setPositiveButton("OK", null)
                        .show()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_ENABLE_BT -> {
                if (resultCode != RESULT_OK) {
                    AlertDialog.Builder(this)
                        .setTitle("Bluetooth Required")
                        .setMessage("Bluetooth must be enabled for this app to function.")
                        .setPositiveButton("OK", null)
                        .show()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothService.disconnect()
    }
}