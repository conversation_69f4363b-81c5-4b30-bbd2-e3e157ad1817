<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.planter" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_main_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="16"/></Target><Target id="@+id/statusBar" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="41" endOffset="18"/></Target><Target id="@+id/tvConnectionStatus" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="29" endOffset="38"/></Target><Target id="@+id/btnConnect" view="Button"><Expressions/><location startLine="31" startOffset="8" endLine="39" endOffset="47"/></Target><Target id="@+id/controlArea" view="LinearLayout"><Expressions/><location startLine="44" startOffset="4" endLine="150" endOffset="18"/></Target><Target id="@+id/leftJoystick" view="com.example.planter.JoystickView"><Expressions/><location startLine="75" startOffset="12" endLine="78" endOffset="47"/></Target><Target id="@+id/btnHorn" view="Button"><Expressions/><location startLine="90" startOffset="12" endLine="98" endOffset="52"/></Target><Target id="@+id/btnLights" view="Button"><Expressions/><location startLine="100" startOffset="12" endLine="108" endOffset="52"/></Target><Target id="@+id/btnStop" view="Button"><Expressions/><location startLine="110" startOffset="12" endLine="118" endOffset="52"/></Target><Target id="@+id/rightJoystick" view="com.example.planter.JoystickView"><Expressions/><location startLine="143" startOffset="12" endLine="146" endOffset="47"/></Target><Target id="@+id/dataArea" view="LinearLayout"><Expressions/><location startLine="153" startOffset="4" endLine="187" endOffset="18"/></Target><Target id="@+id/tvReceivedData" view="TextView"><Expressions/><location startLine="175" startOffset="12" endLine="183" endOffset="39"/></Target></Targets></Layout>