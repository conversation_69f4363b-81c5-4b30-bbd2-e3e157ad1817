-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [androidx.databinding:viewbinding:8.11.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d15d33ea087763d0c21be17c1e0861a7\transformed\viewbinding-8.11.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a4e76919eb039bb1b10323e92398cd9\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa4f8f4de143b79cf04b7aab6d79d80\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\926d96d50179f0ee13b6ed0f5a123c31\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\824944e86c541400809569ce69d5aab9\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c40441935c3933413b19fae5efec462\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e7cce5959bb43e9ed48c844c6f0258b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5c835dbb26fa903b34e7dd4b30ab8a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50b9f5d78059c64868a3e853ab2cc165\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a003f3f6bf265fc505efdb91a5a745a\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b76b587607d3f2fa9b62a52e8b4ac487\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e18d81d43e2113d1b343543401a9d00\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28dd2f8794c6f8471f0cb32ab8993f60\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a636fcbec761ea4e28cbe55a4ba44a8f\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b23689e2b2728a2fb2133d0887a39dc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fdc97ed6d1eb30c71c57dcb722ddd73\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\489ebfe70a9f5f312b82bc4994986787\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef57d1e8b409d9d3bde5e8f2ac69587\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:6:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:7:5-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:7:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:12:5-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:12:22-70
uses-feature#android.hardware.bluetooth
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:14:5-16:35
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:16:9-32
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:15:9-50
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:18:5-39:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:18:5-39:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a4e76919eb039bb1b10323e92398cd9\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a4e76919eb039bb1b10323e92398cd9\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa4f8f4de143b79cf04b7aab6d79d80\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa4f8f4de143b79cf04b7aab6d79d80\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:23:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:21:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:24:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:22:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:19:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:26:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:20:9-65
activity#com.example.planter.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:27:9-38:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:31:13-50
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:30:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:32:13-49
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:33:13-37:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:36:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml:36:27-74
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.11.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d15d33ea087763d0c21be17c1e0861a7\transformed\viewbinding-8.11.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d15d33ea087763d0c21be17c1e0861a7\transformed\viewbinding-8.11.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a4e76919eb039bb1b10323e92398cd9\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a4e76919eb039bb1b10323e92398cd9\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa4f8f4de143b79cf04b7aab6d79d80\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa4f8f4de143b79cf04b7aab6d79d80\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a69f5246a1a71e421f05951a759a13a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85171e4206c16718d7c8d5bf43fe825d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\926d96d50179f0ee13b6ed0f5a123c31\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\926d96d50179f0ee13b6ed0f5a123c31\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\824944e86c541400809569ce69d5aab9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\824944e86c541400809569ce69d5aab9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cd3daaaa7b2a32f64013d0258d8a30a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c40441935c3933413b19fae5efec462\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c40441935c3933413b19fae5efec462\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e7cce5959bb43e9ed48c844c6f0258b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e7cce5959bb43e9ed48c844c6f0258b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5c835dbb26fa903b34e7dd4b30ab8a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb5c835dbb26fa903b34e7dd4b30ab8a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50b9f5d78059c64868a3e853ab2cc165\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50b9f5d78059c64868a3e853ab2cc165\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a003f3f6bf265fc505efdb91a5a745a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a003f3f6bf265fc505efdb91a5a745a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\499cb625e195031490d2b230fd99c673\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\306dbeeedef5ec3722ac3802108b58d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6edc257433ad7f57ca34fc395a7be0d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b76b587607d3f2fa9b62a52e8b4ac487\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b76b587607d3f2fa9b62a52e8b4ac487\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1225ad1152007df66d50dcffcbe26e25\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e18d81d43e2113d1b343543401a9d00\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e18d81d43e2113d1b343543401a9d00\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28dd2f8794c6f8471f0cb32ab8993f60\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28dd2f8794c6f8471f0cb32ab8993f60\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ed89daf239d020270328bf0b2917631\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd811a106770e2ea84bb91143af278b4\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\54c8e97fceebf05a01c2f3ccec507e1e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\82acd443bb8e6b620978dbc588e9d803\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\13aec054922c6b865e26b9010d4084aa\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\750ad70ca7d2f2aadf98cdad3b4c7def\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb2718579edd88a786d10712a0c8e9f\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edce7b8ce0e805ee7581ac75045450a\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a636fcbec761ea4e28cbe55a4ba44a8f\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a636fcbec761ea4e28cbe55a4ba44a8f\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88ce6832dcb1a9564fbc158fb43c8a3\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d714ce27bb382dd5411af92c5af9fa71\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2ae42fb96d605594219ee072ea3d24b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08676af66e63b63b9c97decd248ae0ed\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f9fffeca9b4e75d900913b18ac2df78\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb891f82f60120b0609f05d68d17a84f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b30944cffca17a947fd6a78050968f33\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b23689e2b2728a2fb2133d0887a39dc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b23689e2b2728a2fb2133d0887a39dc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6d2a8a7272352c082e2050a2b41847\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\268192141123e01386248e6368401d3e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b6530e7271de0fec1178f5d19b403e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fdc97ed6d1eb30c71c57dcb722ddd73\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fdc97ed6d1eb30c71c57dcb722ddd73\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\489ebfe70a9f5f312b82bc4994986787\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\489ebfe70a9f5f312b82bc4994986787\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef57d1e8b409d9d3bde5e8f2ac69587\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef57d1e8b409d9d3bde5e8f2ac69587\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Planter\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\59782c46e92902f9e632069eb2572923\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6c1600fc438d4f4ee7e589d45f28b9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.planter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.planter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
