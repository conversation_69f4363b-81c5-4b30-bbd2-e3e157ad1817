  Manifest android  ACCESS_FINE_LOCATION android.Manifest.permission  BLUETOOTH_CONNECT android.Manifest.permission  BLUETOOTH_SCAN android.Manifest.permission  AlertDialog android.app  ActivityCompat android.app.Activity  AlertDialog android.app.Activity  BluetoothAdapter android.app.Activity  BluetoothService android.app.Activity  
ContextCompat android.app.Activity  Float android.app.Activity  Handler android.app.Activity  Intent android.app.Activity  JoystickView android.app.Activity  Looper android.app.Activity  Manifest android.app.Activity  MotionEvent android.app.Activity  PackageManager android.app.Activity  R android.app.Activity  REQUEST_BLUETOOTH_PERMISSIONS android.app.Activity  REQUEST_ENABLE_BT android.app.Activity  	RESULT_OK android.app.Activity  String android.app.Activity  View android.app.Activity  all android.app.Activity  android android.app.Activity  
isNotEmpty android.app.Activity  map android.app.Activity  
mutableListOf android.app.Activity  onActivityResult android.app.Activity  onCreate android.app.Activity  onRequestPermissionsResult android.app.Activity  
runOnUiThread android.app.Activity  sendCommand android.app.Activity  toTypedArray android.app.Activity  JoystickListener !android.app.Activity.JoystickView  widget android.app.Activity.android  
ScrollView #android.app.Activity.android.widget  Builder android.app.AlertDialog  setItems android.app.AlertDialog.Builder  
setMessage android.app.AlertDialog.Builder  setNegativeButton android.app.AlertDialog.Builder  setPositiveButton android.app.AlertDialog.Builder  setTitle android.app.AlertDialog.Builder  show android.app.AlertDialog.Builder  BluetoothAdapter android.bluetooth  BluetoothDevice android.bluetooth  BluetoothSocket android.bluetooth  ACTION_REQUEST_ENABLE "android.bluetooth.BluetoothAdapter  
bondedDevices "android.bluetooth.BluetoothAdapter  cancelDiscovery "android.bluetooth.BluetoothAdapter  getDefaultAdapter "android.bluetooth.BluetoothAdapter  	isEnabled "android.bluetooth.BluetoothAdapter  !createRfcommSocketToServiceRecord !android.bluetooth.BluetoothDevice  name !android.bluetooth.BluetoothDevice  close !android.bluetooth.BluetoothSocket  connect !android.bluetooth.BluetoothSocket  inputStream !android.bluetooth.BluetoothSocket  outputStream !android.bluetooth.BluetoothSocket  Context android.content  Intent android.content  ActivityCompat android.content.Context  AlertDialog android.content.Context  BluetoothAdapter android.content.Context  BluetoothService android.content.Context  
ContextCompat android.content.Context  Float android.content.Context  Handler android.content.Context  Intent android.content.Context  JoystickView android.content.Context  Looper android.content.Context  Manifest android.content.Context  MotionEvent android.content.Context  PackageManager android.content.Context  R android.content.Context  REQUEST_BLUETOOTH_PERMISSIONS android.content.Context  REQUEST_ENABLE_BT android.content.Context  	RESULT_OK android.content.Context  String android.content.Context  View android.content.Context  all android.content.Context  android android.content.Context  
isNotEmpty android.content.Context  map android.content.Context  
mutableListOf android.content.Context  sendCommand android.content.Context  toTypedArray android.content.Context  JoystickListener $android.content.Context.JoystickView  widget android.content.Context.android  
ScrollView &android.content.Context.android.widget  ActivityCompat android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  BluetoothAdapter android.content.ContextWrapper  BluetoothService android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  Float android.content.ContextWrapper  Handler android.content.ContextWrapper  Intent android.content.ContextWrapper  JoystickView android.content.ContextWrapper  Looper android.content.ContextWrapper  Manifest android.content.ContextWrapper  MotionEvent android.content.ContextWrapper  PackageManager android.content.ContextWrapper  R android.content.ContextWrapper  REQUEST_BLUETOOTH_PERMISSIONS android.content.ContextWrapper  REQUEST_ENABLE_BT android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  String android.content.ContextWrapper  View android.content.ContextWrapper  all android.content.ContextWrapper  android android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  map android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  sendCommand android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  JoystickListener +android.content.ContextWrapper.JoystickView  widget &android.content.ContextWrapper.android  
ScrollView -android.content.ContextWrapper.android.widget  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AttributeSet android.graphics  Boolean android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  Float android.graphics  Int android.graphics  JoystickListener android.graphics  JvmOverloads android.graphics  MotionEvent android.graphics  Paint android.graphics  View android.graphics  apply android.graphics  atan2 android.graphics  cos android.graphics  
cyberGreen android.graphics  darkBackground android.graphics  min android.graphics  pow android.graphics  sin android.graphics  sqrt android.graphics  
drawCircle android.graphics.Canvas  
parseColor android.graphics.Color  Paint android.graphics.Paint  Style android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  
cyberGreen android.graphics.Paint  darkBackground android.graphics.Paint  isAntiAlias android.graphics.Paint  setShadowLayer android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  Bundle 
android.os  Handler 
android.os  Looper 
android.os  post android.os.Handler  
getMainLooper android.os.Looper  AttributeSet android.util  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  MotionEvent android.view  View android.view  ActivityCompat  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  BluetoothAdapter  android.view.ContextThemeWrapper  BluetoothService  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  Float  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  JoystickView  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  MotionEvent  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  REQUEST_BLUETOOTH_PERMISSIONS  android.view.ContextThemeWrapper  REQUEST_ENABLE_BT  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  sendCommand  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  JoystickListener -android.view.ContextThemeWrapper.JoystickView  widget (android.view.ContextThemeWrapper.android  
ScrollView /android.view.ContextThemeWrapper.android.widget  
ACTION_CANCEL android.view.MotionEvent  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  Color android.view.View  
FOCUS_DOWN android.view.View  MotionEvent android.view.View  OnClickListener android.view.View  OnTouchListener android.view.View  Paint android.view.View  apply android.view.View  atan2 android.view.View  cos android.view.View  
cyberGreen android.view.View  darkBackground android.view.View  
invalidate android.view.View  min android.view.View  onDraw android.view.View  
onSizeChanged android.view.View  parent android.view.View  post android.view.View  pow android.view.View  setOnClickListener android.view.View  setOnTouchListener android.view.View  sin android.view.View  sqrt android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  Button android.widget  
ScrollView android.widget  TextView android.widget  setOnClickListener android.widget.Button  setOnTouchListener android.widget.Button  text android.widget.Button  
fullScroll android.widget.ScrollView  parent android.widget.TextView  text android.widget.TextView  ActivityCompat #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  BluetoothAdapter #androidx.activity.ComponentActivity  BluetoothService #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  JoystickView #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  MotionEvent #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  REQUEST_BLUETOOTH_PERMISSIONS #androidx.activity.ComponentActivity  REQUEST_ENABLE_BT #androidx.activity.ComponentActivity  	RESULT_OK #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  sendCommand #androidx.activity.ComponentActivity  startActivityForResult #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  JoystickListener 0androidx.activity.ComponentActivity.JoystickView  widget +androidx.activity.ComponentActivity.android  
ScrollView 2androidx.activity.ComponentActivity.android.widget  AppCompatActivity androidx.appcompat.app  ActivityCompat (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  BluetoothAdapter (androidx.appcompat.app.AppCompatActivity  BluetoothService (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  Float (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  JoystickView (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  MotionEvent (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  REQUEST_BLUETOOTH_PERMISSIONS (androidx.appcompat.app.AppCompatActivity  REQUEST_ENABLE_BT (androidx.appcompat.app.AppCompatActivity  	RESULT_OK (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  onActivityResult (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onRequestPermissionsResult (androidx.appcompat.app.AppCompatActivity  sendCommand (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  JoystickListener 5androidx.appcompat.app.AppCompatActivity.JoystickView  widget 0androidx.appcompat.app.AppCompatActivity.android  
ScrollView 7androidx.appcompat.app.AppCompatActivity.android.widget  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  ActivityCompat #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  Array #androidx.core.app.ComponentActivity  BluetoothAdapter #androidx.core.app.ComponentActivity  BluetoothService #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  JoystickView #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  MotionEvent #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  REQUEST_BLUETOOTH_PERMISSIONS #androidx.core.app.ComponentActivity  REQUEST_ENABLE_BT #androidx.core.app.ComponentActivity  	RESULT_OK #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  sendCommand #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  JoystickListener 0androidx.core.app.ComponentActivity.JoystickView  widget +androidx.core.app.ComponentActivity.android  
ScrollView 2androidx.core.app.ComponentActivity.android.widget  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityCompat &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  BluetoothAdapter &androidx.fragment.app.FragmentActivity  BluetoothService &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  Float &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  JoystickView &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  MotionEvent &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  REQUEST_BLUETOOTH_PERMISSIONS &androidx.fragment.app.FragmentActivity  REQUEST_ENABLE_BT &androidx.fragment.app.FragmentActivity  	RESULT_OK &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  onActivityResult &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onRequestPermissionsResult &androidx.fragment.app.FragmentActivity  sendCommand &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  JoystickListener 3androidx.fragment.app.FragmentActivity.JoystickView  widget .androidx.fragment.app.FragmentActivity.android  
ScrollView 5androidx.fragment.app.FragmentActivity.android.widget  ActivityCompat com.example.planter  AlertDialog com.example.planter  AppCompatActivity com.example.planter  Array com.example.planter  AttributeSet com.example.planter  BluetoothAdapter com.example.planter  BluetoothDevice com.example.planter  BluetoothListener com.example.planter  BluetoothService com.example.planter  BluetoothSocket com.example.planter  Boolean com.example.planter  Bundle com.example.planter  Button com.example.planter  	ByteArray com.example.planter  Canvas com.example.planter  Color com.example.planter  Context com.example.planter  
ContextCompat com.example.planter  Float com.example.planter  Handler com.example.planter  IOException com.example.planter  InputStream com.example.planter  Int com.example.planter  IntArray com.example.planter  Intent com.example.planter  JoystickListener com.example.planter  JoystickView com.example.planter  JvmOverloads com.example.planter  List com.example.planter  Log com.example.planter  Looper com.example.planter  MainActivity com.example.planter  Manifest com.example.planter  MotionEvent com.example.planter  OutputStream com.example.planter  PackageManager com.example.planter  Paint com.example.planter  R com.example.planter  REQUEST_BLUETOOTH_PERMISSIONS com.example.planter  REQUEST_ENABLE_BT com.example.planter  	RESULT_OK com.example.planter  SPP_UUID com.example.planter  String com.example.planter  TAG com.example.planter  TextView com.example.planter  Thread com.example.planter  UUID com.example.planter  View com.example.planter  all com.example.planter  android com.example.planter  apply com.example.planter  atan2 com.example.planter  cos com.example.planter  
cyberGreen com.example.planter  darkBackground com.example.planter  	emptyList com.example.planter  invoke com.example.planter  
isNotEmpty com.example.planter  map com.example.planter  min com.example.planter  
mutableListOf com.example.planter  pow com.example.planter  sendCommand com.example.planter  sin com.example.planter  sqrt com.example.planter  toByteArray com.example.planter  toList com.example.planter  toTypedArray com.example.planter  ActivityCompat $com.example.planter.BluetoothService  BluetoothAdapter $com.example.planter.BluetoothService  BluetoothDevice $com.example.planter.BluetoothService  BluetoothListener $com.example.planter.BluetoothService  BluetoothSocket $com.example.planter.BluetoothService  Boolean $com.example.planter.BluetoothService  	ByteArray $com.example.planter.BluetoothService  Context $com.example.planter.BluetoothService  Handler $com.example.planter.BluetoothService  IOException $com.example.planter.BluetoothService  InputStream $com.example.planter.BluetoothService  List $com.example.planter.BluetoothService  Log $com.example.planter.BluetoothService  Looper $com.example.planter.BluetoothService  Manifest $com.example.planter.BluetoothService  OutputStream $com.example.planter.BluetoothService  PackageManager $com.example.planter.BluetoothService  SPP_UUID $com.example.planter.BluetoothService  String $com.example.planter.BluetoothService  TAG $com.example.planter.BluetoothService  Thread $com.example.planter.BluetoothService  UUID $com.example.planter.BluetoothService  bluetoothAdapter $com.example.planter.BluetoothService  bluetoothSocket $com.example.planter.BluetoothService  connectToDevice $com.example.planter.BluetoothService  context $com.example.planter.BluetoothService  
disconnect $com.example.planter.BluetoothService  	emptyList $com.example.planter.BluetoothService  getPairedDevices $com.example.planter.BluetoothService  inputStream $com.example.planter.BluetoothService  invoke $com.example.planter.BluetoothService  isBluetoothAvailable $com.example.planter.BluetoothService  isConnected $com.example.planter.BluetoothService  listener $com.example.planter.BluetoothService  mainHandler $com.example.planter.BluetoothService  outputStream $com.example.planter.BluetoothService  sendCommand $com.example.planter.BluetoothService  setBluetoothListener $com.example.planter.BluetoothService  startListening $com.example.planter.BluetoothService  toByteArray $com.example.planter.BluetoothService  toList $com.example.planter.BluetoothService  onConnectionStatusChanged 6com.example.planter.BluetoothService.BluetoothListener  onDataReceived 6com.example.planter.BluetoothService.BluetoothListener  onError 6com.example.planter.BluetoothService.BluetoothListener  ActivityCompat .com.example.planter.BluetoothService.Companion  BluetoothAdapter .com.example.planter.BluetoothService.Companion  	ByteArray .com.example.planter.BluetoothService.Companion  Handler .com.example.planter.BluetoothService.Companion  Log .com.example.planter.BluetoothService.Companion  Looper .com.example.planter.BluetoothService.Companion  Manifest .com.example.planter.BluetoothService.Companion  PackageManager .com.example.planter.BluetoothService.Companion  SPP_UUID .com.example.planter.BluetoothService.Companion  String .com.example.planter.BluetoothService.Companion  TAG .com.example.planter.BluetoothService.Companion  Thread .com.example.planter.BluetoothService.Companion  UUID .com.example.planter.BluetoothService.Companion  	emptyList .com.example.planter.BluetoothService.Companion  invoke .com.example.planter.BluetoothService.Companion  toByteArray .com.example.planter.BluetoothService.Companion  toList .com.example.planter.BluetoothService.Companion  AttributeSet  com.example.planter.JoystickView  Boolean  com.example.planter.JoystickView  Canvas  com.example.planter.JoystickView  Color  com.example.planter.JoystickView  Context  com.example.planter.JoystickView  Float  com.example.planter.JoystickView  Int  com.example.planter.JoystickView  JoystickListener  com.example.planter.JoystickView  JvmOverloads  com.example.planter.JoystickView  MotionEvent  com.example.planter.JoystickView  Paint  com.example.planter.JoystickView  apply  com.example.planter.JoystickView  atan2  com.example.planter.JoystickView  backgroundPaint  com.example.planter.JoystickView  	basePaint  com.example.planter.JoystickView  
baseRadius  com.example.planter.JoystickView  centerX  com.example.planter.JoystickView  centerY  com.example.planter.JoystickView  cos  com.example.planter.JoystickView  
cyberGreen  com.example.planter.JoystickView  darkBackground  com.example.planter.JoystickView  
invalidate  com.example.planter.JoystickView  
isDragging  com.example.planter.JoystickView  	knobPaint  com.example.planter.JoystickView  
knobRadius  com.example.planter.JoystickView  knobX  com.example.planter.JoystickView  knobY  com.example.planter.JoystickView  listener  com.example.planter.JoystickView  min  com.example.planter.JoystickView  pow  com.example.planter.JoystickView  setJoystickListener  com.example.planter.JoystickView  sin  com.example.planter.JoystickView  sqrt  com.example.planter.JoystickView  updateKnobPosition  com.example.planter.JoystickView  onJoystickMoved 1com.example.planter.JoystickView.JoystickListener  onJoystickReleased 1com.example.planter.JoystickView.JoystickListener  ActivityCompat  com.example.planter.MainActivity  AlertDialog  com.example.planter.MainActivity  Array  com.example.planter.MainActivity  BluetoothAdapter  com.example.planter.MainActivity  BluetoothService  com.example.planter.MainActivity  Boolean  com.example.planter.MainActivity  Bundle  com.example.planter.MainActivity  Button  com.example.planter.MainActivity  
ContextCompat  com.example.planter.MainActivity  Float  com.example.planter.MainActivity  Handler  com.example.planter.MainActivity  Int  com.example.planter.MainActivity  IntArray  com.example.planter.MainActivity  Intent  com.example.planter.MainActivity  JoystickView  com.example.planter.MainActivity  Looper  com.example.planter.MainActivity  Manifest  com.example.planter.MainActivity  MotionEvent  com.example.planter.MainActivity  PackageManager  com.example.planter.MainActivity  R  com.example.planter.MainActivity  REQUEST_BLUETOOTH_PERMISSIONS  com.example.planter.MainActivity  REQUEST_ENABLE_BT  com.example.planter.MainActivity  	RESULT_OK  com.example.planter.MainActivity  String  com.example.planter.MainActivity  TextView  com.example.planter.MainActivity  View  com.example.planter.MainActivity  all  com.example.planter.MainActivity  android  com.example.planter.MainActivity  bluetoothService  com.example.planter.MainActivity  
btnConnect  com.example.planter.MainActivity  btnHorn  com.example.planter.MainActivity  	btnLights  com.example.planter.MainActivity  btnStop  com.example.planter.MainActivity  checkBluetoothEnabled  com.example.planter.MainActivity  checkBluetoothPermissions  com.example.planter.MainActivity  currentCommand  com.example.planter.MainActivity  findViewById  com.example.planter.MainActivity  initializeBluetooth  com.example.planter.MainActivity  initializeViews  com.example.planter.MainActivity  isHoldingButton  com.example.planter.MainActivity  
isNotEmpty  com.example.planter.MainActivity  leftJoystick  com.example.planter.MainActivity  map  com.example.planter.MainActivity  
mutableListOf  com.example.planter.MainActivity  
rightJoystick  com.example.planter.MainActivity  
runOnUiThread  com.example.planter.MainActivity  sendCommand  com.example.planter.MainActivity  setContentView  com.example.planter.MainActivity  setupButtons  com.example.planter.MainActivity  setupHoldButton  com.example.planter.MainActivity  setupJoysticks  com.example.planter.MainActivity  showDeviceSelectionDialog  com.example.planter.MainActivity  startActivityForResult  com.example.planter.MainActivity  toTypedArray  com.example.planter.MainActivity  tvConnectionStatus  com.example.planter.MainActivity  tvReceivedData  com.example.planter.MainActivity  ActivityCompat *com.example.planter.MainActivity.Companion  AlertDialog *com.example.planter.MainActivity.Companion  BluetoothAdapter *com.example.planter.MainActivity.Companion  BluetoothService *com.example.planter.MainActivity.Companion  
ContextCompat *com.example.planter.MainActivity.Companion  Handler *com.example.planter.MainActivity.Companion  Intent *com.example.planter.MainActivity.Companion  Looper *com.example.planter.MainActivity.Companion  Manifest *com.example.planter.MainActivity.Companion  MotionEvent *com.example.planter.MainActivity.Companion  PackageManager *com.example.planter.MainActivity.Companion  R *com.example.planter.MainActivity.Companion  REQUEST_BLUETOOTH_PERMISSIONS *com.example.planter.MainActivity.Companion  REQUEST_ENABLE_BT *com.example.planter.MainActivity.Companion  	RESULT_OK *com.example.planter.MainActivity.Companion  View *com.example.planter.MainActivity.Companion  all *com.example.planter.MainActivity.Companion  
isNotEmpty *com.example.planter.MainActivity.Companion  map *com.example.planter.MainActivity.Companion  
mutableListOf *com.example.planter.MainActivity.Companion  sendCommand *com.example.planter.MainActivity.Companion  toTypedArray *com.example.planter.MainActivity.Companion  JoystickListener -com.example.planter.MainActivity.JoystickView  widget (com.example.planter.MainActivity.android  
ScrollView /com.example.planter.MainActivity.android.widget  
btnConnect com.example.planter.R.id  btnHorn com.example.planter.R.id  	btnLights com.example.planter.R.id  btnStop com.example.planter.R.id  leftJoystick com.example.planter.R.id  
rightJoystick com.example.planter.R.id  tvConnectionStatus com.example.planter.R.id  tvReceivedData com.example.planter.R.id  
activity_main com.example.planter.R.layout  widget com.example.planter.android  
ScrollView "com.example.planter.android.widget  IOException java.io  InputStream java.io  OutputStream java.io  message java.io.IOException  close java.io.InputStream  read java.io.InputStream  close java.io.OutputStream  flush java.io.OutputStream  write java.io.OutputStream  Runnable 	java.lang  Thread 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  start java.lang.Thread  ActivityCompat 	java.util  BluetoothAdapter 	java.util  BluetoothDevice 	java.util  BluetoothListener 	java.util  BluetoothSocket 	java.util  Boolean 	java.util  	ByteArray 	java.util  Context 	java.util  Handler 	java.util  IOException 	java.util  InputStream 	java.util  List 	java.util  Log 	java.util  Looper 	java.util  Manifest 	java.util  OutputStream 	java.util  PackageManager 	java.util  SPP_UUID 	java.util  String 	java.util  TAG 	java.util  Thread 	java.util  UUID 	java.util  	emptyList 	java.util  invoke 	java.util  toByteArray 	java.util  toList 	java.util  
fromString java.util.UUID  Array kotlin  	ByteArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  apply kotlin  map kotlin  toList kotlin  toString 
kotlin.Any  not kotlin.Boolean  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  pow kotlin.Float  times kotlin.Float  
unaryMinus kotlin.Float  	compareTo 
kotlin.Int  div 
kotlin.Int  all kotlin.IntArray  
isNotEmpty kotlin.IntArray  	Companion 
kotlin.String  invoke 
kotlin.String  toByteArray 
kotlin.String  invoke kotlin.String.Companion  message kotlin.Throwable  List kotlin.collections  MutableList kotlin.collections  all kotlin.collections  	emptyList kotlin.collections  
isNotEmpty kotlin.collections  map kotlin.collections  min kotlin.collections  
mutableListOf kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toTypedArray kotlin.collections  get kotlin.collections.List  isEmpty kotlin.collections.List  map kotlin.collections.List  toTypedArray kotlin.collections.List  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  JvmOverloads 
kotlin.jvm  AttributeSet kotlin.math  Boolean kotlin.math  Canvas kotlin.math  Color kotlin.math  Context kotlin.math  Float kotlin.math  Int kotlin.math  JoystickListener kotlin.math  JvmOverloads kotlin.math  MotionEvent kotlin.math  Paint kotlin.math  View kotlin.math  apply kotlin.math  atan2 kotlin.math  cos kotlin.math  
cyberGreen kotlin.math  darkBackground kotlin.math  min kotlin.math  pow kotlin.math  sin kotlin.math  sqrt kotlin.math  Sequence kotlin.sequences  all kotlin.sequences  map kotlin.sequences  min kotlin.sequences  toList kotlin.sequences  String kotlin.text  all kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  min kotlin.text  toByteArray kotlin.text  toList kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            